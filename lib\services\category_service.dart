import 'package:flutter/foundation.dart' hide Category;
import 'package:hive_flutter/hive_flutter.dart';
import 'package:my_fincance_app/models/category.dart';
import 'package:uuid/uuid.dart';

class CategoryService extends ChangeNotifier {
  final String _userId;
  final _uuid = Uuid();
  static const String _boxName = 'categories';

  List<Category> _categories = [];
  bool _isLoading = false;

  CategoryService(this._userId) {
    if (_userId.isNotEmpty) {
      _loadCategories();
    }
  }

  List<Category> get categories => _categories;
  bool get isLoading => _isLoading;

  Future<void> _loadCategories() async {
    _isLoading = true;
    notifyListeners();

    final box = await Hive.openBox<Category>(_boxName);
    _categories = box.values.where((c) => c.userId == _userId).toList();
    _categories.sort((a, b) => a.name.compareTo(b.name));

    // Initialize default categories if none exist for the user
    if (_categories.isEmpty && _userId.isNotEmpty) {
      await _initializeDefaultCategories();
      // Reload after initialization
      _categories = box.values.where((c) => c.userId == _userId).toList();
      _categories.sort((a, b) => a.name.compareTo(b.name));
    }

    _isLoading = false;
    notifyListeners();
  }

  Future<void> addCategory(String name, String type) async {
    if (_userId.isEmpty) return;

    final box = Hive.box<Category>(_boxName);
    final newCategory = Category()
      ..id = _uuid.v4()
      ..name = name
      ..type = type
      ..userId = _userId
      ..createdAt = DateTime.now()
      ..updatedAt = DateTime.now();

    await box.put(newCategory.id, newCategory);
    await _loadCategories();
  }

  Future<void> updateCategory(
    Category category,
    String newName,
    String newType,
  ) async {
    category.name = newName;
    category.type = newType;
    category.updatedAt = DateTime.now();

    await category.save();
    await _loadCategories();
  }

  Future<void> deleteCategory(Category category) async {
    await category.delete();
    await _loadCategories();
  }

  Future<void> _initializeDefaultCategories() async {
    if (_userId.isEmpty) return;
    final box = Hive.box<Category>(_boxName);

    final defaultIncomeCategories = [
      'معاش',
      'کسب و کار',
      'سرمایه‌گذاری',
      'هدیه',
      'سایر درآمدها',
    ];

    final defaultExpenseCategories = [
      'غذا و نوشیدنی',
      'حمل و نقل',
      'خرید',
      'تفریح',
      'بهداشت و درمان',
      'آموزش',
      'خانه',
      'لباس',
      'سایر مصارف',
    ];

    for (final categoryName in defaultIncomeCategories) {
      final category = Category()
        ..id = _uuid.v4()
        ..name = categoryName
        ..type = 'درآمد'
        ..userId = _userId
        ..createdAt = DateTime.now()
        ..updatedAt = DateTime.now();
      await box.put(category.id, category);
    }

    for (final categoryName in defaultExpenseCategories) {
      final category = Category()
        ..id = _uuid.v4()
        ..name = categoryName
        ..type = 'مصرف'
        ..userId = _userId
        ..createdAt = DateTime.now()
        ..updatedAt = DateTime.now();
      await box.put(category.id, category);
    }
  }

  Future<void> refresh() async {
    await _loadCategories();
  }
}
