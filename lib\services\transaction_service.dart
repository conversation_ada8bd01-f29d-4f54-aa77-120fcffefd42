import 'package:flutter/material.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:my_fincance_app/models/transaction.dart';
import 'package:my_fincance_app/services/loan_service.dart';
import 'package:my_fincance_app/utils/date_formatter.dart';
import 'package:uuid/uuid.dart';

class TransactionService extends ChangeNotifier {
  final String _userId;
  final _uuid = Uuid();
  LoanService? _loanService;
  static const String _boxName = 'transactions';

  List<Transaction> _transactions = [];
  bool _isLoading = false;

  TransactionService(this._userId) {
    if (_userId.isNotEmpty) {
      _loadTransactions();
    }
  }

  void setLoanService(LoanService loanService) {
    _loanService = loanService;
  }

  List<Transaction> get transactions => _transactions;
  bool get isLoading => _isLoading;

  List<Transaction> get currentMonthTransactions {
    final now = DateTime.now();
    return _transactions
        .where((t) => DateFormatter.isSameMonth(t.date, now))
        .toList();
  }

  List<Transaction> get currentMonthIncome =>
      currentMonthTransactions.where((t) => t.type == 'درآمد').toList();

  List<Transaction> get currentMonthExpenses =>
      currentMonthTransactions.where((t) => t.type == 'مصرف').toList();

  double get totalCurrentMonthIncome =>
      currentMonthIncome.fold<double>(0.0, (sum, t) => sum + t.amount);

  double get totalCurrentMonthExpenses =>
      currentMonthExpenses.fold<double>(0.0, (sum, t) => sum + t.amount);

  double get currentMonthNetSavings =>
      totalCurrentMonthIncome - totalCurrentMonthExpenses;

  Future<void> _loadTransactions() async {
    _isLoading = true;
    notifyListeners();

    final box = await Hive.openBox<Transaction>(_boxName);
    _transactions = box.values.where((t) => t.userId == _userId).toList();
    _transactions.sort((a, b) => b.date.compareTo(a.date));

    _isLoading = false;
    notifyListeners();
  }

  Future<void> addTransaction(
    String description,
    double amount,
    String type,
    String categoryId, {
    String? loanId,
    DateTime? date,
  }) async {
    if (_userId.isEmpty) return;

    final box = Hive.box<Transaction>(_boxName);
    final newTransaction = Transaction()
      ..id = _uuid.v4()
      ..date = date ?? DateTime.now()
      ..description = description
      ..amount = amount
      ..type = type
      ..categoryId = categoryId
      ..loanId = loanId
      ..userId = _userId
      ..createdAt = DateTime.now()
      ..updatedAt = DateTime.now();

    await box.put(newTransaction.id, newTransaction);
    await _loadTransactions();

    if (loanId != null) {
      _loanService?.updateLoanStatuses();
    }
  }

  Future<void> updateTransaction(
    Transaction transaction,
    String description,
    double amount,
    String type,
    String categoryId, {
    String? loanId,
    DateTime? date,
  }) async {
    final oldLoanId = transaction.loanId;

    transaction.description = description;
    transaction.amount = amount;
    transaction.type = type;
    transaction.categoryId = categoryId;
    transaction.loanId = loanId;
    transaction.date = date ?? transaction.date;
    transaction.updatedAt = DateTime.now();

    await transaction.save();
    await _loadTransactions();

    if (oldLoanId != null || loanId != null) {
      _loanService?.updateLoanStatuses();
    }
  }

  Future<void> deleteTransaction(Transaction transaction) async {
    final loanId = transaction.loanId;
    await transaction.delete();
    await _loadTransactions();

    if (loanId != null) {
      _loanService?.updateLoanStatuses();
    }
  }

  Map<String, double> getCurrentMonthExpensesByCategory() {
    final expenses = currentMonthExpenses;
    final Map<String, double> categoryTotals = {};

    for (final transaction in expenses) {
      categoryTotals[transaction.categoryId] =
          (categoryTotals[transaction.categoryId] ?? 0.0) + transaction.amount;
    }
    return categoryTotals;
  }

  /// Get all-time financial summary (for dashboard)
  Map<String, double> get allTimeFinancialSummary {
    return {
      'totalIncome': totalIncome,
      'totalExpenses': totalExpenses,
      'netSavings': totalCapital,
    };
  }

  double get totalCapital => totalIncome - totalExpenses;

  double get totalIncome => _transactions
      .where((t) => t.type == 'درآمد')
      .fold<double>(0.0, (sum, t) => sum + t.amount);

  double get totalExpenses => _transactions
      .where((t) => t.type == 'مصرف')
      .fold<double>(0.0, (sum, t) => sum + t.amount);

  List<Transaction> getTransactionsForLoan(String loanId) {
    return _transactions.where((t) => t.loanId == loanId).toList();
  }

  Future<void> refresh() async {
    await _loadTransactions();
  }
}
