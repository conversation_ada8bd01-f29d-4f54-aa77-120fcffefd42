import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:hive_flutter/hive_flutter.dart';
import 'package:my_fincance_app/models/budget.dart';
import 'package:my_fincance_app/models/category.dart';
import 'package:my_fincance_app/models/loan.dart';
import 'package:my_fincance_app/models/transaction.dart';
import 'package:my_fincance_app/models/user.dart';
import 'package:path_provider/path_provider.dart';

class HiveService {
  static Future<void> init() async {
    // For web, Hive uses IndexedDB which is already initialized.
    // For mobile, we need to specify a directory.
    if (kIsWeb) {
      await Hive.initFlutter();
    } else {
      final appDocumentDir = await getApplicationDocumentsDirectory();
      await Hive.initFlutter(appDocumentDir.path);
    }

    // Registering adapters
    Hive.registerAdapter(UserAdapter());
    Hive.registerAdapter(CategoryAdapter());
    Hive.registerAdapter(LoanAdapter());
    Hive.registerAdapter(TransactionAdapter());
    Hive.registerAdapter(BudgetAdapter());
  }
}
