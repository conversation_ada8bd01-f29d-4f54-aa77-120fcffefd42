import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:my_fincance_app/core/services/hive_service.dart';
import 'package:my_fincance_app/pages/auth/login_page.dart';
import 'package:my_fincance_app/pages/home_page.dart';
import 'package:my_fincance_app/services/budget_service.dart';
import 'package:my_fincance_app/services/capital_service.dart';
import 'package:my_fincance_app/services/category_service.dart';
import 'package:my_fincance_app/services/hive_auth_service.dart';
import 'package:my_fincance_app/services/loan_service.dart';
import 'package:my_fincance_app/services/transaction_service.dart';
import 'package:provider/provider.dart';

void main() async {
  // Ensure Flutter is initialized
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize Hive for local storage
  await HiveService.init();

  // Initialize the local authentication service
  final authService = HiveAuthService();
  await authService.initialize();

  runApp(
    MultiProvider(
      providers: [
        // Provide the authentication service
        ChangeNotifierProvider.value(value: authService),

        // Provide other services that depend on the authenticated user
        ChangeNotifierProxyProvider<HiveAuthService, CategoryService>(
          create: (context) => CategoryService(''),
          update: (context, auth, previous) =>
              CategoryService(auth.currentUserId),
        ),
        ChangeNotifierProxyProvider<HiveAuthService, LoanService>(
          create: (context) => LoanService(''),
          update: (context, auth, previous) => LoanService(auth.currentUserId),
        ),
        ChangeNotifierProxyProvider2<
          HiveAuthService,
          LoanService,
          TransactionService
        >(
          create: (context) => TransactionService(''),
          update: (context, auth, loanService, previous) {
            final transactionService = TransactionService(auth.currentUserId);
            transactionService.setLoanService(loanService);
            return transactionService;
          },
        ),
        ChangeNotifierProxyProvider<HiveAuthService, BudgetService>(
          create: (context) => BudgetService(''),
          update: (context, auth, previous) =>
              BudgetService(auth.currentUserId),
        ),
        ChangeNotifierProxyProvider2<
          TransactionService,
          LoanService,
          CapitalService
        >(
          create: (context) => CapitalService(
            Provider.of<TransactionService>(context, listen: false),
            Provider.of<LoanService>(context, listen: false),
          ),
          update: (context, transactionService, loanService, previous) =>
              CapitalService(transactionService, loanService),
        ),
      ],
      child: const MyApp(),
    ),
  );
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'مرکز مالی من',
      theme: ThemeData(
        primarySwatch: Colors.blue,
        textTheme: GoogleFonts.lalezarTextTheme(Theme.of(context).textTheme),
      ),
      localizationsDelegates: const [
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],
      supportedLocales: const [
        Locale('fa', ''), // Persian
      ],
      locale: const Locale('fa', ''),
      home: Consumer<HiveAuthService>(
        builder: (context, authService, child) {
          if (!authService.isInitialized) {
            // Show a loading screen while the auth service is initializing
            return const Scaffold(
              body: Center(child: CircularProgressIndicator()),
            );
          }
          // Navigate to HomePage if logged in, otherwise to LoginPage
          return authService.isLoggedIn ? const HomePage() : const LoginPage();
        },
      ),
      debugShowCheckedModeBanner: false,
    );
  }
}
