import 'dart:convert';
import 'package:crypto/crypto.dart';
import 'package:flutter/widgets.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:my_fincance_app/models/user.dart';
import 'package:uuid/uuid.dart';

class HiveAuthService extends ChangeNotifier {
  User? _currentUser;
  bool _isInitialized = false;
  final _uuid = Uuid();
  static const String _userBoxName = 'users';
  static const String _sessionBoxName = 'session';

  User? get currentUser => _currentUser;
  bool get isLoggedIn => _currentUser != null;
  String get currentUserId => _currentUser?.id ?? '';
  bool get isInitialized => _isInitialized;

  /// Initialize the auth service
  Future<void> initialize() async {
    if (_isInitialized) return;

    // Open boxes
    await Hive.openBox<User>(_userBoxName);
    await Hive.openBox<String>(_sessionBoxName);

    // Check for a logged-in user session
    final sessionBox = Hive.box<String>(_sessionBoxName);
    final currentUserId = sessionBox.get('currentUserId');

    if (currentUserId != null) {
      final userBox = Hive.box<User>(_userBoxName);
      _currentUser = userBox.get(currentUserId);
    }

    _isInitialized = true;
    notifyListeners();
  }

  /// Hash a password using SHA-256
  String _hashPassword(String password) {
    final bytes = utf8.encode(password);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }

  /// Register a new user
  Future<bool> register(String email, String password, String name) async {
    final userBox = Hive.box<User>(_userBoxName);

    // Check if user already exists
    if (userBox.values.any((user) => user.email == email)) {
      return false; // User with this email already exists
    }

    final newUser = User()
      ..id = _uuid.v4()
      ..email = email
      ..name = name
      ..password = _hashPassword(password)
      ..createdAt = DateTime.now()
      ..updatedAt = DateTime.now();

    await userBox.put(newUser.id, newUser);
    return true;
  }

  /// Login user
  Future<bool> login(String email, String password) async {
    final userBox = Hive.box<User>(_userBoxName);
    final hashedPassword = _hashPassword(password);

    try {
      final user = userBox.values.firstWhere(
        (user) => user.email == email && user.password == hashedPassword,
      );

      _currentUser = user;

      // Save session
      final sessionBox = Hive.box<String>(_sessionBoxName);
      await sessionBox.put('currentUserId', user.id);

      notifyListeners();
      return true;
    } catch (e) {
      // User not found or password incorrect
      return false;
    }
  }

  /// Logout user
  Future<void> logout() async {
    _currentUser = null;
    final sessionBox = Hive.box<String>(_sessionBoxName);
    await sessionBox.delete('currentUserId');
    notifyListeners();
  }

  /// Update user profile
  Future<bool> updateProfile(String name) async {
    if (_currentUser == null) return false;

    _currentUser!.name = name;
    _currentUser!.updatedAt = DateTime.now();
    await _currentUser!.save(); // Save the updated user object to Hive

    notifyListeners();
    return true;
  }

  /// Change password
  Future<bool> changePassword(
    String currentPassword,
    String newPassword,
  ) async {
    if (_currentUser == null) return false;

    // Verify current password
    if (_currentUser!.password != _hashPassword(currentPassword)) {
      return false; // Incorrect current password
    }

    _currentUser!.password = _hashPassword(newPassword);
    _currentUser!.updatedAt = DateTime.now();
    await _currentUser!.save();

    return true;
  }
}
