import 'package:flutter/foundation.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:my_fincance_app/models/loan.dart';
import 'package:my_fincance_app/models/transaction.dart';
import 'package:uuid/uuid.dart';

class LoanService extends ChangeNotifier {
  final String _userId;
  final _uuid = Uuid();
  static const String _loansBoxName = 'loans';
  static const String _transactionsBoxName = 'transactions';

  List<Loan> _loans = [];
  List<Transaction> _transactions = [];
  bool _isLoading = false;

  LoanService(this._userId) {
    if (_userId.isNotEmpty) {
      _loadLoans();
    }
  }

  List<Loan> get loans => _loans;
  List<Loan> get debtLoans => _loans.where((l) => l.type == 'بدهی').toList();
  List<Loan> get creditLoans => _loans.where((l) => l.type == 'طلب').toList();
  List<Loan> get activeLoans =>
      _loans.where((l) => l.status == 'فعال').toList();
  bool get isLoading => _isLoading;

  Future<void> _loadLoans() async {
    _isLoading = true;
    notifyListeners();

    final loansBox = await Hive.openBox<Loan>(_loansBoxName);
    _loans = loansBox.values.where((l) => l.userId == _userId).toList();
    _loans.sort((a, b) => b.createdAt.compareTo(a.createdAt));

    final transactionsBox = await Hive.openBox<Transaction>(
      _transactionsBoxName,
    );
    _transactions = transactionsBox.values
        .where((t) => t.userId == _userId && t.loanId != null)
        .toList();

    await _updateLoanStatuses();

    _isLoading = false;
    notifyListeners();
  }

  double getRemainingBalance(String loanId) {
    try {
      final loan = _loans.firstWhere((l) => l.id == loanId);
      final loanTransactions = _transactions.where((t) => t.loanId == loanId);
      final totalPaid = loanTransactions.fold<double>(
        0.0,
        (sum, t) => sum + t.amount,
      );
      return loan.initialAmount - totalPaid;
    } catch (e) {
      return 0.0;
    }
  }

  double getTotalActiveDebt() {
    return debtLoans
        .where((loan) => loan.status == 'فعال')
        .fold<double>(0.0, (sum, loan) => sum + getRemainingBalance(loan.id));
  }

  double getTotalActiveCredit() {
    return creditLoans
        .where((loan) => loan.status == 'فعال')
        .fold<double>(0.0, (sum, loan) => sum + getRemainingBalance(loan.id));
  }

  Future<void> _updateLoanStatuses() async {
    bool changed = false;
    for (final loan in _loans) {
      if (loan.status == 'فعال') {
        final remainingBalance = getRemainingBalance(loan.id);
        if (remainingBalance <= 0) {
          loan.status = 'تمام شده';
          loan.updatedAt = DateTime.now();
          await loan.save();
          changed = true;
        }
      }
    }
    if (changed) {
      notifyListeners();
    }
  }

  double getLoanProgress(String loanId) {
    try {
      final loan = _loans.firstWhere((l) => l.id == loanId);
      final remainingBalance = getRemainingBalance(loanId);
      final paidAmount = loan.initialAmount - remainingBalance;
      return loan.initialAmount > 0
          ? (paidAmount / loan.initialAmount).clamp(0.0, 1.0)
          : 0.0;
    } catch (e) {
      return 0.0;
    }
  }

  Future<void> addLoan(
    String name,
    String type,
    String person,
    double initialAmount,
    DateTime startDate,
  ) async {
    if (_userId.isEmpty) return;

    final box = Hive.box<Loan>(_loansBoxName);
    final newLoan = Loan()
      ..id = _uuid.v4()
      ..name = name
      ..type = type
      ..person = person
      ..initialAmount = initialAmount
      ..startDate = startDate
      ..status = 'فعال'
      ..userId = _userId
      ..createdAt = DateTime.now()
      ..updatedAt = DateTime.now();

    await box.put(newLoan.id, newLoan);
    await _loadLoans();
  }

  Future<void> updateLoanStatuses() async {
    await _updateLoanStatuses();
    notifyListeners();
  }

  Future<void> updateLoan(
    Loan loan,
    String newName,
    String newType,
    String newPerson,
    double newInitialAmount,
    DateTime newStartDate,
  ) async {
    loan.name = newName;
    loan.type = newType;
    loan.person = newPerson;
    loan.initialAmount = newInitialAmount;
    loan.startDate = newStartDate;
    loan.updatedAt = DateTime.now();

    await loan.save();
    await _loadLoans();
  }

  Future<void> deleteLoan(Loan loan) async {
    await loan.delete();
    await _loadLoans();
  }

  Future<void> refresh() async {
    await _loadLoans();
  }
}
