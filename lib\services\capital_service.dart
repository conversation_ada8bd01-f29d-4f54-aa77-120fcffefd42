import 'package:flutter/foundation.dart';
import 'package:my_fincance_app/services/transaction_service.dart';
import 'package:my_fincance_app/services/loan_service.dart';

class CapitalService extends ChangeNotifier {
  final TransactionService _transactionService;
  final LoanService _loanService;

  CapitalService(this._transactionService, this._loanService) {
    _transactionService.addListener(_onDataChanged);
    _loanService.addListener(_onDataChanged);
  }

  void _onDataChanged() {
    notifyListeners();
  }

  @override
  void dispose() {
    _transactionService.removeListener(_onDataChanged);
    _loanService.removeListener(_onDataChanged);
    super.dispose();
  }

  double get liquidCapital => _transactionService.totalCapital;
  double get totalAssets => liquidCapital + _loanService.getTotalActiveCredit();
  double get totalLiabilities => _loanService.getTotalActiveDebt();
  double get netWorth => totalAssets - totalLiabilities;

  Map<String, double> get capitalBreakdown => {
    'liquid_capital': liquidCapital,
    'active_credits': _loanService.getTotalActiveCredit(),
    'active_debts': _loanService.getTotalActiveDebt(),
    'net_worth': netWorth,
  };

  double get financialHealthScore {
    final totalIncome = _transactionService.totalIncome;
    if (totalIncome == 0) return 0;
    final savingsRate = (_transactionService.totalCapital) / totalIncome;
    final debtToIncomeRatio = totalLiabilities / totalIncome;
    double score = 50 + (savingsRate * 50) - (debtToIncomeRatio * 20);
    return score.clamp(0, 100);
  }

  String get financialHealthStatus {
    final score = financialHealthScore;
    if (score >= 80) return 'عالی';
    if (score >= 60) return 'خوب';
    if (score >= 40) return 'متوسط';
    return 'ضعیف';
  }

  List<String> get recommendedActions {
    final recommendations = <String>[];
    if (financialHealthScore < 40) {
      recommendations.add('وضعیت مالی شما نیاز به بهبود دارد');
    }
    if (liquidCapital < _transactionService.totalExpenses) {
      recommendations.add('سعی کنید پس‌انداز اضطراری خود را افزایش دهید');
    }
    if (totalLiabilities > totalAssets * 0.5) {
      recommendations.add(
        'بدهی‌های شما زیاد است، برای کاهش آن‌ها برنامه‌ریزی کنید',
      );
    }
    if (recommendations.isEmpty) {
      recommendations.add('وضعیت مالی شما مناسب است، ادامه دهید!');
    }
    return recommendations;
  }
}
