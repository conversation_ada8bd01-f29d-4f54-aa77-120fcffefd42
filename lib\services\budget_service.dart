import 'package:flutter/foundation.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:my_fincance_app/models/budget.dart';
import 'package:my_fincance_app/models/transaction.dart';
import 'package:my_fincance_app/utils/date_formatter.dart';
import 'package:uuid/uuid.dart';

class BudgetService extends ChangeNotifier {
  final String _userId;
  final _uuid = Uuid();
  static const String _budgetsBoxName = 'budgets';
  static const String _transactionsBoxName = 'transactions';

  List<Budget> _budgets = [];
  List<Transaction> _transactions = [];
  bool _isLoading = false;

  BudgetService(this._userId) {
    if (_userId.isNotEmpty) {
      _loadBudgets();
    }
  }

  List<Budget> get budgets => _budgets;
  bool get isLoading => _isLoading;

  List<Budget> get currentMonthBudgets {
    final now = DateTime.now();
    return _budgets
        .where((b) => DateFormatter.isSameMonth(b.period, now))
        .toList();
  }

  Future<void> _loadBudgets() async {
    _isLoading = true;
    notifyListeners();

    final budgetsBox = await Hive.openBox<Budget>(_budgetsBoxName);
    _budgets = budgetsBox.values.where((b) => b.userId == _userId).toList();
    _budgets.sort((a, b) => b.period.compareTo(a.period));

    final transactionsBox = await Hive.openBox<Transaction>(
      _transactionsBoxName,
    );
    _transactions = transactionsBox.values
        .where((t) => t.userId == _userId && t.type == 'مصرف')
        .toList();

    _isLoading = false;
    notifyListeners();
  }

  double getActualSpending(String categoryId, DateTime period) {
    return _transactions
        .where(
          (t) =>
              t.categoryId == categoryId &&
              DateFormatter.isSameMonth(t.date, period),
        )
        .fold<double>(0.0, (sum, t) => sum + t.amount);
  }

  double getBudgetProgress(Budget budget) {
    final actualSpending = getActualSpending(budget.categoryId, budget.period);
    return budget.amount > 0 ? actualSpending / budget.amount : 0.0;
  }

  bool isBudgetExceeded(Budget budget) {
    return getBudgetProgress(budget) > 1.0;
  }

  Map<String, Map<String, double>> getCurrentMonthBudgetVsActual() {
    final result = <String, Map<String, double>>{};
    for (final budget in currentMonthBudgets) {
      final actualSpending = getActualSpending(
        budget.categoryId,
        budget.period,
      );
      result[budget.categoryId] = {
        'budget': budget.amount,
        'actual': actualSpending,
        'remaining': budget.amount - actualSpending,
      };
    }
    return result;
  }

  Future<void> addBudget(
    DateTime period,
    double amount,
    String categoryId,
  ) async {
    if (_userId.isEmpty) return;

    final box = Hive.box<Budget>(_budgetsBoxName);
    final newBudget = Budget()
      ..id = _uuid.v4()
      ..period = period
      ..amount = amount
      ..categoryId = categoryId
      ..userId = _userId
      ..createdAt = DateTime.now()
      ..updatedAt = DateTime.now();

    await box.put(newBudget.id, newBudget);
    await _loadBudgets();
  }

  Future<void> updateBudget(
    Budget budget,
    DateTime newPeriod,
    double newAmount,
  ) async {
    budget.period = newPeriod;
    budget.amount = newAmount;
    budget.updatedAt = DateTime.now();

    await budget.save();
    await _loadBudgets();
  }

  Future<void> deleteBudget(Budget budget) async {
    await budget.delete();
    await _loadBudgets();
  }

  Future<void> refresh() async {
    await _loadBudgets();
  }
}
